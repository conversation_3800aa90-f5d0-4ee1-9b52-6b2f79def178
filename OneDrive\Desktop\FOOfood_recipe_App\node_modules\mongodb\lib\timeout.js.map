{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../src/timeout.ts"], "names": [], "mappings": ";;;AAAA,mCAAkD;AAGlD,mCAAmG;AAEnG,mCAAqD;AAErD,gBAAgB;AAChB,MAAa,YAAa,SAAQ,KAAK;IAErC,IAAa,IAAI;QACf,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,YAAY,OAAe,EAAE,OAA4C;QACvE,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,EAAE,CAAC,KAAc;QACtB,OAAO,CACL,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,CAC/F,CAAC;IACJ,CAAC;CACF;AAhBD,oCAgBC;AAID;;;;;KAKK;AACL,MAAa,OAAQ,SAAQ,OAAc;IASzC,IAAI,aAAa;QACf,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC;QACzC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;IACpD,CAAC;IAED,yDAAyD;IACzD,YACE,WAAqB,GAAG,EAAE,CAAC,IAAI,EAC/B,OAA+D;QAE/D,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC;QAC/B,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,CAAC;QAErC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,iCAAyB,CAAC,kDAAkD,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,MAAe,CAAC;QACpB,KAAK,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE;YACzB,MAAM,GAAG,aAAa,CAAC;YAEvB,QAAQ,CAAC,YAAI,EAAE,aAAa,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAjCE,UAAK,GAAkB,IAAI,CAAC;QAE3B,aAAQ,GAAG,KAAK,CAAC;QAClB,YAAO,GAAG,KAAK,CAAC;QAgCrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAE3C,IAAI,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,EAAE,GAAG,IAAA,mBAAU,EAAC,GAAG,EAAE;gBACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,MAAM,CAAC,IAAI,YAAY,CAAC,iBAAiB,QAAQ,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;YACxE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClB,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,KAAK,UAAU,IAAI,KAAK,EAAE,CAAC;gBACjD,uDAAuD;gBACvD,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,SAAS,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAA,qBAAY,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,cAAc;QACZ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,6GAA6G;YAC7G,kGAAkG;YAClG,8DAA8D;YAC9D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAW,CAAC,CAAC;YAClC,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,OAAO,CAAC,QAAgB,EAAE,KAAY;QAClD,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAU,MAAM,CAAC,SAAiB;QACtC,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IACzE,CAAC;CACF;AAtFD,0BAsFC;AAqBD,SAAS,6BAA6B,CAAC,CAAU;IAC/C,OAAO,CACL,CAAC,IAAI,IAAI;QACT,OAAO,CAAC,KAAK,QAAQ;QACrB,0BAA0B,IAAI,CAAC;QAC/B,OAAO,CAAC,CAAC,wBAAwB,KAAK,QAAQ;QAC9C,oBAAoB,IAAI,CAAC;QACzB,OAAO,CAAC,CAAC,kBAAkB,KAAK,QAAQ,CACzC,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B,CAAC,CAAU;IAC7C,OAAO,CACL,CAAC,IAAI,IAAI;QACT,OAAO,CAAC,KAAK,QAAQ;QACrB,0BAA0B,IAAI,CAAC;QAC/B,OAAO,CAAC,CAAC,wBAAwB,KAAK,QAAQ;QAC9C,WAAW,IAAI,CAAC;QAChB,OAAO,CAAC,CAAC,SAAS,KAAK,QAAQ,CAChC,CAAC;AACJ,CAAC;AAED,gBAAgB;AAChB,MAAsB,cAAc;IAClC,MAAM,CAAC,MAAM,CAAC,OAA8B;QAC1C,IAAI,OAAO,CAAC,OAAO,EAAE,cAAc,IAAI,IAAI;YAAE,OAAO,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC;QACpF,IAAI,2BAA2B,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC;aAC5E,IAAI,6BAA6B,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;;YACrF,MAAM,IAAI,yBAAiB,CAAC,sBAAsB,CAAC,CAAC;IAC3D,CAAC;CA0BF;AAhCD,wCAgCC;AAED,gBAAgB;AAChB,MAAa,kBAAmB,SAAQ,cAAc;IAYpD,YAAY,OAAkC;QAC5C,KAAK,EAAE,CAAC;QAJH,qBAAgB,GAAG,CAAC,CAAC;QAK1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEnC,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,wBAAwB,CAAC;QAEjE,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAE/C,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACtD,CAAC;IAED,IAAI,eAAe;QACjB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9D,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC;IACtE,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,sBAAsB;QACxB,sBAAsB;QACtB,IAAI,OAAO,IAAI,CAAC,uBAAuB,KAAK,QAAQ,IAAI,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,CAAC;YAC9F,MAAM,EAAE,eAAe,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC;YAC3D,IAAI,eAAe,IAAI,CAAC;gBACtB,OAAO,OAAO,CAAC,MAAM,CACnB,IAAI,kCAA0B,CAAC,uCAAuC,IAAI,CAAC,SAAS,IAAI,CAAC,CAC1F,CAAC;YACJ,MAAM,6BAA6B,GACjC,wBAAwB,KAAK,CAAC;gBAC9B,IAAA,eAAO,EAAC,eAAe,EAAE,wBAAwB,CAAC,KAAK,wBAAwB,CAAC;YAClF,IAAI,6BAA6B,EAAE,CAAC;gBAClC,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACN,IAAI,eAAe,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;oBAC5D,IAAI,CAAC,uBAAuB,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;gBAClE,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,IAAI,yBAAyB;QAC3B,IACE,OAAO,IAAI,CAAC,0BAA0B,KAAK,QAAQ;YACnD,IAAI,CAAC,0BAA0B,EAAE,OAAO,EACxC,CAAC;YACD,IAAI,OAAO,IAAI,CAAC,uBAAuB,KAAK,QAAQ,EAAE,CAAC;gBACrD,kBAAkB;gBAClB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,uBAAuB,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,yBAAiB,CACzB,oGAAoG,CACrG,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,0BAA0B,CAAC;IACzC,CAAC;IAED,IAAI,qBAAqB;QACvB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,OAAO,IAAI,CAAC;QACnD,IAAI,eAAe,GAAG,CAAC;YAAE,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACjE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,kCAA0B,CAAC,+BAA+B,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,IAAI,oBAAoB;QACtB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;YAAE,OAAO,IAAI,CAAC;QACnD,IAAI,eAAe,GAAG,CAAC;YAAE,OAAO,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACjE,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,kCAA0B,CAAC,8BAA8B,CAAC,CAAC,CAAC;IACxF,CAAC;IAED,OAAO;QACL,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,uBAAuB,EAAE,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,0BAA0B,EAAE,KAAK,EAAE,CAAC;IAC3C,CAAC;IAED,KAAK;QACH,IAAI,CAAC,uBAAuB,EAAE,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,0BAA0B,EAAE,KAAK,EAAE,CAAC;IAC3C,CAAC;IAED;;;;QAII;IACJ,yBAAyB,CAAC,OAAgB;QACxC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QACjC,IAAI,eAAe,IAAI,CAAC;YACtB,MAAM,IAAI,kCAA0B,CAAC,OAAO,IAAI,iBAAiB,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC;QACvF,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,MAAM,cAAc,GAAG,IAAI,kBAAkB,CAAC;YAC5C,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;SACxD,CAAC,CAAC;QACH,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAClC,OAAO,cAAc,CAAC;IACxB,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;IAEQ,qBAAqB,CAAC,OAAiB,EAAE,OAAoC;QACpF,IAAI,OAAO,CAAC,aAAa;YAAE,OAAO;QAClC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/D,IAAI,SAAS,GAAG,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IACjF,CAAC;IAEQ,kBAAkB;QACzB,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAjJD,gDAiJC;AAED,gBAAgB;AAChB,MAAa,oBAAqB,SAAQ,cAAc;IAItD,YAAY,OAAoC;QAC9C,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED,WAAW;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,sBAAsB;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,wBAAwB,GAAG,CAAC;YAC5F,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,yBAAyB;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,CAAC;YAChF,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,qBAAqB;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,OAAO;IACT,CAAC;IAED,KAAK;QACH,OAAO;IACT,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEQ,qBAAqB,CAAC,QAAkB,EAAE,QAAqC;QACtF,6DAA6D;IAC/D,CAAC;IAEQ,kBAAkB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;IACtC,CAAC;CACF;AAzDD,oDAyDC"}